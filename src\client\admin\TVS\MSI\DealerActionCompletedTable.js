import React, { useEffect, useState, useRef, useMemo, useCallback } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { DateTime } from 'luxon';
import { Tag } from 'primereact/tag';
import { getDate } from '../../../../components/BGHF/helper';
import { Dialog } from 'primereact/dialog';
import { useSelector } from 'react-redux';
import { MultiSelect } from 'primereact/multiselect';
import { InputText } from 'primereact/inputtext';
import { Calendar } from 'primereact/calendar';
import APIServices from '../../../../service/APIService';
import { API } from '../../../../constants/api_url';
import moment from 'moment';
import ActionofDealerStatus from './ActionofDealerStatus';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import { FilterService } from 'primereact/api';


const DealerActionCompletedTable = ({ data, dealerList, assessorList, globalFilter, editDealer }) => {
    const select = useSelector((state) => state.userlist);
    const [databk, setDatabk] = useState([]);
    const [datas, setDatas] = useState([]);
    const [search, setSearch] = useState('');
    const [dateFilter, setDateFilter] = useState({ start: null, end: null });
    const [actionReportData, setActionReportData] = useState(null);
    const [actionStatusReport, setActionStatusReport] = useState(false);
    const [actionDetails, setActionDetails] = useState(null);
    const [actionDetailsDialog, setActionDetailsDialog] = useState(false);
    const [filteredDataCount, setFilteredDataCount] = useState(0);
    const [currentFilteredData, setCurrentFilteredData] = useState([]);
    const dataTableRef = useRef(null);

    // Add filter state management for DataTable
    const [tableFilters, setTableFilters] = useState({
        dealerName: { matchMode: 'in', value: null },
        dealerLocation: { matchMode: 'in', value: null },
        dealerZone: { matchMode: 'in', value: null },
        dealerCategory: { matchMode: 'in', value: null },
        status: { matchMode: 'in', value: null },
        calibrationTeamMember: { matchMode: 'in', value: null }
    });

    // Define applyFilters function before using it in useEffect
    const applyFilters = (dataToFilter, searchValue = search) => {
        // Extract and group actions by trackId from all dealers
        let actionsByTrackId = {};

        dataToFilter.forEach(dealer => {
            if (Array.isArray(dealer.actions) && dealer.actions.length > 0) {
                // Group actions by trackId
                const groupedActions = {};
                dealer.actions.forEach(action => {
                    const trackId = action.trackId || 'untracked-' + Math.random().toString(36).substring(2, 9);
                    if (!groupedActions[trackId]) {
                        groupedActions[trackId] = [];
                    }
                    groupedActions[trackId].push({
                        ...action,
                        dealer: dealer,
                        dealerName: dealer?.vendor?.dealerName || 'NA',
                        dealerLocation: dealer?.vendor?.dealerLocation || 'NA',
                        dealerCode: dealer?.vendor?.code || 'NA',
                        dealerZone: dealer?.vendor?.dealerZone,
                        dealerCategory: dealer?.vendor?.dealerCategory,
                        calibrationId: 'MSI-' + (dealer?.vendor?.code || 'NA') + '-' +
                            DateTime.fromISO(dealer.created_on, { zone: 'utc' }).toLocal().toFormat('ddMMyyyy'),
                        calibrationScore: JSON.parse(dealer?.dealerAuditorChecklistSubmission?.score || '{overallScore:-}')?.overallScore,
                        calibrationDate: dealer?.auditStartDate,
                        calibrationTeamMember: getCalibrationTeamMember(dealer, assessorList)
                    });
                });

                // Check each action group if it's completed
                Object.entries(groupedActions).forEach(([trackId, actions]) => {
                    // Check if all "Checklist Submission" actions are completed
                    const submissionActions = actions.filter(action => action.actionType === "Checklist Submission");
                    const allSubmissionActionsCompleted = submissionActions.length === 0 ||
                        submissionActions.every(action => action.status === 'Completed' || action.status === 'completed');

                    // Check if all "Checklist Submission Review" actions are completed
                    const reviewActions = actions.filter(action => action.actionType === "Checklist Submission Review");
                    const allReviewActionsCompleted = reviewActions.length === 0 ||
                        reviewActions.every(action => action.status === 'Completed' || action.status === 'completed');

                    // Group is considered completed if both conditions are met
                    const isCompleted = allSubmissionActionsCompleted && allReviewActionsCompleted;

                    if (isCompleted) {
                        // Sort actions by created date (newest first)
                        actions.sort((a, b) => {
                            const dateA = moment(a.created_on || a.createdDate);
                            const dateB = moment(b.created_on || b.createdDate);
                            return dateB - dateA;
                        });

                        // Use the first action as the representative for the group
                        const primaryAction = actions[0];

                        // Add group information
                        primaryAction.actionsInGroup = actions.length;
                        primaryAction.trackId = trackId;
                        primaryAction.actionGroup = actions;

                        // Store in the global collection
                        actionsByTrackId[trackId] = primaryAction;
                    }
                });
            }
        });

        // Convert to array
        let allActions = Object.values(actionsByTrackId);

        // Apply search filter to the actions
        if (searchValue) {
            allActions = allActions.filter(action =>
                (action.dealerName && action.dealerName.toLowerCase().includes(searchValue.toLowerCase())) ||
                (action.dealerCode && action.dealerCode.toLowerCase().includes(searchValue.toLowerCase())) ||
                (action.description && action.description.toLowerCase().includes(searchValue.toLowerCase()))
            );
        }

        // Apply date range filter
        if (dateFilter.start && dateFilter.end) {
            allActions = allActions.filter(action => {
                const dateStr = action.calibrationDate;
                if (!dateStr) return true;

                const itemDate = DateTime.fromISO(dateStr, { zone: 'utc' }).toJSDate();
                const startDate = moment(dateFilter.start).startOf('day').toDate();
                const endDate = moment(dateFilter.end).endOf('day').toDate();

                return itemDate >= startDate && itemDate <= endDate;
            });
        }

        // Add tableIndex property for sorting
        const indexedData = allActions.map((item, index) => ({
            ...item,
            tableIndex: index + 1
        }));

        setDatas(indexedData);
    };

    // Helper function to get calibration team member
    const getCalibrationTeamMember = (dealer, assessorList) => {
        if (dealer?.dealerAuditorChecklistSubmission) {
            let findId = dealer?.dealerAuditorChecklistSubmission?.modified_by ||
                dealer?.dealerAuditorChecklistSubmission?.created_by || null;
            return assessorList.find(i => i.id === findId)?.information?.empname || '';
        } else {
            return 'Not Assigned';
        }
    };

    useEffect(() => {
        // Process the data to extract actions
        const processedData = data.filter(item => Array.isArray(item.actions) && item.actions.length > 0);
        setDatabk(processedData);
        applyFilters(processedData);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [data]);

    useEffect(() => {
        applyFilters(databk);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [dateFilter]);

    // Memoize the enhanced data to prevent unnecessary re-computations
    const enhancedData = useMemo(() => {
        return datas.map((x, index) => ({
            ...x,
            tableIndex: index + 1
        }));
    }, [datas]);

    // Manual filter calculation function as a fallback
    const calculateFilteredCount = useCallback(() => {
        if (!enhancedData || enhancedData.length === 0) return 0;

        let filteredDataLocal = [...enhancedData];

        // Apply table filters
        Object.entries(tableFilters).forEach(([field, filter]) => {
            if (filter.value && filter.value.length > 0) {
                filteredDataLocal = filteredDataLocal.filter(item => {
                    const fieldValue = item[field];
                    if (fieldValue == null) return false;
                    return filter.value.includes(fieldValue);
                });
            }
        });

        // Apply global filter if present
        if (globalFilter && globalFilter.trim()) {
            const globalFilterLower = globalFilter.toLowerCase();
            filteredDataLocal = filteredDataLocal.filter(item => {
                return Object.values(item).some(value =>
                    value && value.toString().toLowerCase().includes(globalFilterLower)
                );
            });
        }

        return filteredDataLocal.length;
    }, [enhancedData, tableFilters, globalFilter]);

    // Initialize filtered count when enhanced data changes
    useEffect(() => {
        // Only update if no filters are currently applied
        const hasActiveFilters = Object.values(tableFilters).some(filter =>
            filter.value && filter.value.length > 0
        );

        if (!hasActiveFilters && !globalFilter) {
            setFilteredDataCount(enhancedData.length);
            console.log('Enhanced data changed, updating count to:', enhancedData.length);
        }
    }, [enhancedData.length, tableFilters, globalFilter]);

    // Backup mechanism to ensure count accuracy
    useEffect(() => {
        const updateCountFromCalculation = () => {
            const calculatedCount = calculateFilteredCount();

            if (calculatedCount !== filteredDataCount) {
                console.log('🔄 Manual calculation: Updating count to:', calculatedCount);
                setFilteredDataCount(calculatedCount);
            }
        };

        // Check periodically for any missed updates
        const interval = setInterval(updateCountFromCalculation, 3000);

        return () => clearInterval(interval);
    }, [calculateFilteredCount, filteredDataCount]);

    // Also trigger manual calculation when filters change
    useEffect(() => {
        const calculatedCount = calculateFilteredCount();
        console.log('🔢 Filters changed, calculated count:', calculatedCount);

        // Small delay to allow DataTable to process first
        const timeout = setTimeout(() => {
            if (calculatedCount !== filteredDataCount) {
                console.log('🔄 Filter change: Updating count to:', calculatedCount);
                setFilteredDataCount(calculatedCount);
            }
        }, 500);

        return () => clearTimeout(timeout);
    }, [tableFilters, globalFilter, calculateFilteredCount]);

    const zonalOfficeList = [{ name: "Central", value: 1 }, { name: "East", value: 2 }, { name: "North", value: 3 }, { name: "South", value: 9 }, { name: "South1", value: 4 }, { name: "South2", value: 5 }, { name: "West", value: 8 }, { name: "West1", value: 6 }, { name: "West2", value: 7 }, { name: "TN", value: 10 }, { name: "North1", value: 11 }, { name: "North2", value: 12 }]


    const dealerType = [{ name: 'Authorized Main Dealer', value: 1 }, { name: 'Authorized Dealer', value: 2 }, { name: 'Authorized Parts Stockist (APS)', value: 3 }, { name: 'Area Office', value: 4 }]
    const searchFn = (e) => {
        let val = e.target.value;
        setSearch(val);
        applyFilters(databk, val);
    };



    // View action details - now only used indirectly through description clicks
    const viewActionDetails = (action) => {
        // If this is a grouped action, show the dealer action report instead
        if (action.actionGroup && action.actionGroup.length > 0) {
            setActionReportData({
                ...action.dealer,
                actions: action.actionGroup
            });
            setActionStatusReport(true);
        } else {
            // Otherwise show the individual action details
            setActionDetails(action);
            setActionDetailsDialog(true);
        }
    };

    // Template for action description
    const actionDescriptionTemplate = (rowData) => {
        const description = rowData.description || 'No description available';
        // Truncate description if it's too long
        const truncatedDesc = description.length > 100 ? description.substring(0, 100) + '...' : description;

        // Add action group count if available
        return (
            <div>
                <span
                    style={{ cursor: 'pointer', color: '#0D5EAF', textDecoration: 'underline' }}
                    onClick={() => viewActionDetails(rowData)}
                >
                    {truncatedDesc}
                </span>
                {rowData.actionsInGroup > 1 && (
                    <div className="mt-1">
                        <Tag severity="info" value={`${rowData.actionsInGroup} actions in this group`} />
                    </div>
                )}
            </div>
        );
    };

    // Template for action to be taken
    const actionToBeTemplate = (rowData) => {
        const actionToBeTaken = rowData.actionToBeTaken || 'No action specified';
        // Truncate if it's too long
        return actionToBeTaken.length > 100 ? actionToBeTaken.substring(0, 100) + '...' : actionToBeTaken;
    };



    // Template for action completion date
    const actionCompletionDateTemplate = (rowData) => {
        const completionDate = rowData.completed_on || rowData.modified_on;
        if (!completionDate) return <span>Unknown</span>;

        return DateTime.fromISO(completionDate).toFormat('dd-MM-yyyy');
    };

    // Template for action created date
    const actionCreatedDateTemplate = (rowData) => {
        if (!rowData.created_on) return <span>Unknown</span>;
        return DateTime.fromISO(rowData.created_on).toFormat('dd-MM-yyyy');
    };

    // Template for dealer name
    const dealerNameTemplate = (rowData) => {
        return rowData.dealerName || 'NA';
    };

    // Template for dealer location
    const dealerLocationTemplate = (rowData) => {
        return rowData.dealerLocation || 'NA';
    };

    // Template for dealer zone
    const dealerZoneTemplate = (rowData) => {
        return zonalOfficeList.find(x => x.value === rowData.dealerZone)?.name || 'NA';
    };

    // Template for dealer category
    const dealerCategoryTemplate = (rowData) => {
        return dealerType.find(x => x.value === rowData.dealerCategory)?.name || 'NA';
    };

    // Template for calibration ID
    const calibrationIdTemplate = (rowData) => {
        return (
            <span style={{ cursor: 'pointer', color: '#0D5EAF' }} onClick={() => dealerActionReport(rowData.dealer)}>
                {rowData.calibrationId}
            </span>
        );
    };

    // Template for calibration score
    const calibrationScoreTemplate = (rowData) => {
        return rowData.calibrationScore || '-';
    };

    // Template for calibration team member
    const calibrationTeamMemberTemplate = (rowData) => {

        return rowData.calibrationTeamMember || 'Not Assigned';
    };

    // View dealer action report
    const dealerActionReport = (dealer) => {
        setActionReportData(dealer);
        setActionStatusReport(true);
    };

    // Template for action assigned to
    const actionAssignedToTemplate = (rowData) => {
        return rowData.assigned_to ?
            assessorList.find(i => i.id === rowData.assigned_to)?.information?.empname || 'Unknown' :
            'Not Assigned';
    };

    // Template for action completed by
    const actionCompletedByTemplate = (rowData) => {
        return rowData.completed_by ?
            assessorList.find(i => i.id === rowData.completed_by)?.information?.empname || 'Unknown' :
            (rowData.modified_by ?
                assessorList.find(i => i.id === rowData.modified_by)?.information?.empname || 'Unknown' :
                'Unknown');
    };

    // Sort function for calibration score
    const sortCalibrationScore = (e) => {
        if (e.order === 1) { // ascending
            return e.data.sort((a, b) => {
                const scoreA = a.calibrationScore === '-' ? -1 : parseFloat(a.calibrationScore);
                const scoreB = b.calibrationScore === '-' ? -1 : parseFloat(b.calibrationScore);

                if (scoreA === -1 && scoreB === -1) return 0;
                if (scoreA === -1) return 1;
                if (scoreB === -1) return -1;

                return scoreA - scoreB;
            });
        } else { // descending
            return e.data.sort((a, b) => {
                const scoreA = a.calibrationScore === '-' ? -1 : parseFloat(a.calibrationScore);
                const scoreB = b.calibrationScore === '-' ? -1 : parseFloat(b.calibrationScore);

                if (scoreA === -1 && scoreB === -1) return 0;
                if (scoreA === -1) return 1;
                if (scoreB === -1) return -1;

                return scoreB - scoreA;
            });
        }
    };

    // Sort function for dates
    const sortDate = (e, dateField) => {
        if (e.order === 1) { // ascending
            return e.data.sort((a, b) => {
                const dateA = a[dateField] ? DateTime.fromISO(a[dateField]) : null;
                const dateB = b[dateField] ? DateTime.fromISO(b[dateField]) : null;

                if (!dateA && !dateB) return 0;
                if (!dateA) return 1;
                if (!dateB) return -1;

                return dateA < dateB ? -1 : dateA > dateB ? 1 : 0;
            });
        } else { // descending
            return e.data.sort((a, b) => {
                const dateA = a[dateField] ? DateTime.fromISO(a[dateField]) : null;
                const dateB = b[dateField] ? DateTime.fromISO(b[dateField]) : null;

                if (!dateA && !dateB) return 0;
                if (!dateA) return 1;
                if (!dateB) return -1;

                return dateA > dateB ? -1 : dateA < dateB ? 1 : 0;
            });
        }
    };

    // Sort function for calibration date
    const sortCalibrationDate = (e) => {
        return sortDate(e, 'calibrationDate');
    };

    // Sort function for completion date
    const sortCompletionDate = (e) => {
        return sortDate(e, 'completed_on');
    };

    // Sort function for created date
    const sortCreatedDate = (e) => {
        return sortDate(e, 'created_on');
    };

    // Filter template for dropdown fields
    const RowFilterTemplate = (options, field) => {
        // Get unique values for the field
        const uniqueValues = Array.from(new Set(datas.map(item => {
            if (field === 'dealerZone') {
                const zone = zonalOfficeList.find(x => x.value === item[field]);
                return zone ? zone.name : null;
            } else if (field === 'dealerCategory') {
                const category = dealerType.find(x => x.value === item[field]);
                return category ? category.name : null;
            } else {
                return item[field];
            }
        }))).filter(x => x);

        // Create options for the dropdown
        const dropdownOptions = uniqueValues.map(value => ({ label: value, value }));

        return (
            <MultiSelect
                value={options.value}
                options={dropdownOptions}
                onChange={(e) => options.filterCallback(e.value)}
                placeholder="Any"
                filter
                panelClassName='hidefilter'
                className="p-column-filter"
                maxSelectedLabels={1}
                style={{ minWidth: "14rem" }}
            />
        );
    };

    // Sort function for S.No column
    const sortIndexColumn = (e) => {
        const { data, order } = e;

        // Create a new array with the current data and add an index property
        const indexedData = data.map((item, index) => ({
            ...item,
            tableIndex: index + 1
        }));

        // Sort based on the index
        if (order === 1) { // ascending
            return indexedData.sort((a, b) => a.tableIndex - b.tableIndex);
        } else { // descending
            return indexedData.sort((a, b) => b.tableIndex - a.tableIndex);
        }
    };

    const clearDateFilter = () => {
        setDateFilter({ start: null, end: null });
    };

    const exportExcel = () => {
        if (!datas || datas.length === 0) {
            alert('No data to export.');
            return;
        }

        // Prepare clean data
        const exportData = datas.map((item) => ({
            'S.No': item.tableIndex || '',
            'Action To Be Taken': item.actionToBeTaken || '',
            'Description': item.description || '',
            'Actions in Group': item.actionsInGroup || 1,
            'Track ID': item.trackId || 'N/A',
            'Completion Date': item.completed_on ? DateTime.fromISO(item.completed_on).toFormat('dd-MM-yyyy') : '',
            'Created Date': item.created_on ? DateTime.fromISO(item.created_on).toFormat('dd-MM-yyyy') : '',
            'Completed By': item.completed_by ? (assessorList.find(i => i.id === item.completed_by)?.information?.empname || 'Unknown') : (item.modified_by ? (assessorList.find(i => i.id === item.modified_by)?.information?.empname || 'Unknown') : 'Unknown'),
            'Assigned To': item.assigned_to ? (assessorList.find(i => i.id === item.assigned_to)?.information?.empname || 'Unknown') : 'Not Assigned',
            'Dealer Name': item.dealerName || '',
            'Location': item.dealerLocation || '',
            'Zone': zonalOfficeList.find(x => x.value === item.dealerZone)?.name || '',
            'Category': dealerType.find(x => x.value === item.dealerCategory)?.name || '',
            'Calibration ID': item.calibrationId || '',
            'MSI Score': item.calibrationScore || '',
            'Calibration Date': item.calibrationDate ? DateTime.fromISO(item.calibrationDate).toFormat('dd-MM-yyyy') : '',
            'Calibration Team Member': item.calibrationTeamMember || ''
        }));

        // Create worksheet
        const worksheet = XLSX.utils.json_to_sheet(exportData);

        // Create workbook and append worksheet
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Completed Actions');

        // Generate buffer
        const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });

        // Save file
        const dataBlob = new Blob([excelBuffer], { type: 'application/octet-stream' });
        saveAs(dataBlob, `Completed_Actions_${moment().format('YYYYMMDD_HHmmss')}.xlsx`);
    };

    return (
        <>
            <div className="col-12 flex justify-content-between align-items-center mb-3">
                <div className="col-6 flex gap-3 align-items-center">
                    <div className="flex flex-column">
                        <label className="mb-1">Calibration Date From</label>
                        <Calendar
                            value={dateFilter.start}
                            onChange={(e) => setDateFilter({ ...dateFilter, start: e.value })}
                            placeholder="Start Date"
                            dateFormat="dd-mm-yy"
                            showIcon
                        />
                    </div>
                    <div className="flex flex-column">
                        <label className="mb-1">To</label>
                        <Calendar
                            value={dateFilter.end}
                            onChange={(e) => setDateFilter({ ...dateFilter, end: e.value })}
                            placeholder="End Date"
                            dateFormat="dd-mm-yy"
                            showIcon
                            minDate={dateFilter.start}
                            disabled={!dateFilter.start}
                        />
                    </div>
                    {(dateFilter.start || dateFilter.end) && (
                        <button
                            className="btn btn-sm btn-outline-secondary align-self-end mb-1"
                            onClick={clearDateFilter}
                            style={{ height: '36px' }}
                        >
                            Clear
                        </button>
                    )}
                </div>
                <div className='col-5'>
                    <span className="p-input-icon-left" style={{ width: '100%' }}>
                        <i className="pi pi-search" />
                        <InputText
                            value={search}
                            style={{ width: '100%' }}
                            onChange={searchFn}
                            placeholder="Search Dealer, Description"
                        />
                    </span>
                </div>
            </div>

            <div className="d-flex justify-content-between align-items-center mb-3">
                <h4>Completed Actions ({filteredDataCount})</h4>
                <button
                    className="btn btn-sm btn-success"
                    onClick={exportExcel}
                >
                    Download Excel
                </button>
            </div>


            <DataTable
                ref={dataTableRef}
                value={enhancedData}
                paginator
                rows={10}
                 rowsPerPageOptions={[10, 25, 50, 100,150,200]}
                scrollable
                scrollHeight="500px"
                filters={tableFilters}
                filterDisplay="menu"
                onPage={(e) => {
                    console.log('📄 Page changed:', e);
                    // Ensure count remains accurate when navigating pages
                    const currentCount = calculateFilteredCount();
                    if (currentCount !== filteredDataCount) {
                        console.log('🔄 Page change: Updating count to:', currentCount);
                        setFilteredDataCount(currentCount);
                    }
                }}
                onFilter={(e) => {
                    console.log('🔍 DataTable onFilter triggered');
                    console.log('Event object:', e);

                    // Create a copy of the filters object
                    const cleanedFilters = { ...e.filters };

                    if (cleanedFilters.hasOwnProperty('null')) {
                        delete cleanedFilters['null'];
                    }

                    setTableFilters(cleanedFilters);

                    // Update filtered data count based on the actual filtered results
                    // e.filteredValue contains ALL filtered records across all pages
                    let filteredCount;

                    if (e.filteredValue && Array.isArray(e.filteredValue)) {
                        filteredCount = e.filteredValue.length;
                        console.log('✅ Using e.filteredValue.length:', filteredCount);
                    } else {
                        // Fallback to manual calculation
                        filteredCount = calculateFilteredCount();
                        console.log('⚠️ Fallback to manual calculation:', filteredCount);
                    }

                    console.log('🔢 Filter applied:', cleanedFilters);
                    console.log('🔢 Final filtered count (total across all pages):', filteredCount);
                    console.log('🔢 Enhanced data length:', enhancedData.length);

                    // Update the count and filtered data immediately
                    setFilteredDataCount(filteredCount);
                    setCurrentFilteredData(e.filteredValue || enhancedData);
                }}
                globalFilter={globalFilter}
                className="mt-2 h-500"
                emptyMessage="No completed actions found"
                rowHover
            >
                <Column
                    sortable
                    field="tableIndex"
                    header="S.No"
                    body={(rowData, options) => rowData.tableIndex || options.rowIndex + 1}
                    sortFunction={sortIndexColumn}
                    style={{ width: '60px' }}
                />
                <Column
                    field="actionToBeTaken"
                    header="Action To Be Taken"
                    body={actionToBeTemplate}
                    style={{ width: '200px' }}
                />

                <Column
                    field="description"
                    header="Description"
                    body={actionDescriptionTemplate}
                    style={{ width: '250px' }}
                />

                <Column
                    sortable
                    field="completed_on"
                    header="Completion Date"
                    body={actionCompletionDateTemplate}
                    sortFunction={sortCompletionDate}
                    style={{ width: '130px' }}
                />
                <Column
                    sortable
                    field="created_on"
                    header="Created Date"
                    body={actionCreatedDateTemplate}
                    sortFunction={sortCreatedDate}
                    style={{ width: '120px' }}
                />
                <Column
                    field="completed_by"
                    header="Completed By"
                    body={actionCompletedByTemplate}
                    style={{ width: '150px' }}
                />
                <Column
                    field="assigned_to"
                    header="Assigned To"
                    body={actionAssignedToTemplate}
                    style={{ width: '150px' }}
                />
                <Column
                    sortable
                    field="dealerName"
                    header="Dealer Name"
                    body={dealerNameTemplate}
                    filter
                    filterElement={(options) => RowFilterTemplate(options, "dealerName")}
                    style={{ width: '180px' }}
                />
                <Column
                    sortable
                    field="dealerLocation"
                    header="Location"
                    body={dealerLocationTemplate}
                    filter
                    filterElement={(options) => RowFilterTemplate(options, "dealerLocation")}
                    style={{ width: '150px' }}
                />
                <Column
                    sortable
                    field="dealerZone"
                    header="Zone"
                    body={dealerZoneTemplate}
                    filter
                    filterElement={(options) => RowFilterTemplate(options, "dealerZone")}
                    style={{ width: '120px' }}
                />
                <Column
                    sortable
                    field="dealerCategory"
                    header="Category"
                    body={dealerCategoryTemplate}
                    filter
                    filterElement={(options) => RowFilterTemplate(options, "dealerCategory")}
                    style={{ width: '150px' }}
                />
                <Column
                    sortable
                    field="calibrationId"
                    header="Calibration ID"
                    body={calibrationIdTemplate}
                    style={{ width: '150px' }}
                />
                <Column
                    sortable
                    field="calibrationScore"
                    header="MSI Score"
                    body={calibrationScoreTemplate}
                    sortFunction={sortCalibrationScore}
                    style={{ width: '100px' }}
                />
                <Column
                    sortable
                    field="calibrationDate"
                    header="Calibration Date"
                    body={(rowData) => getDate(rowData.calibrationDate)}
                    sortFunction={sortCalibrationDate}
                    style={{ width: '130px' }}
                />
                <Column
                    field="calibrationTeamMember"
                    header="Calibration Team"
                    body={calibrationTeamMemberTemplate}
                    style={{ width: '150px' }}
                />
            </DataTable>

            {/* Dialog for viewing dealer action report */}
            <Dialog
                visible={actionStatusReport}
                style={{ width: '90%' }}
                className="custom-dialog"
                header="Dealer Action Report"
                onHide={() => { setActionStatusReport(false) }}
            >
                <ActionofDealerStatus report={actionReportData} />
            </Dialog>

            {/* Dialog for viewing action details */}
            <Dialog
                visible={actionDetailsDialog}
                style={{ width: '70%' }}
                className="custom-dialog"
                header="Action Details"
                onHide={() => { setActionDetailsDialog(false) }}
            >
                {actionDetails && (
                    <div className="p-grid">
                        <div className="p-col-12">
                            <h3>{actionDetails.title || 'No Title'}</h3>
                        </div>
                        <div className="p-col-12 p-md-6">
                            <h4>Description</h4>
                            <p>{actionDetails.description || 'No description available'}</p>
                        </div>
                        <div className="p-col-12 p-md-6">
                            <h4>Status</h4>
                            <Tag
                                className={
                                    actionDetails.status === 'completed' || actionDetails.status === 'submitted' ?
                                        'status-tag-green' :
                                        actionDetails.status === 'in progress' ?
                                            'status-tag-orange' :
                                            'status-tag-gray'
                                }
                            >
                                {actionDetails.status || 'unknown'}
                            </Tag>
                        </div>
                        <div className="p-col-12 p-md-6">
                            <h4>Completion Date</h4>
                            <p>{actionDetails.completed_on ?
                                DateTime.fromISO(actionDetails.completed_on).toFormat('dd-MM-yyyy') :
                                'Not completed'}</p>
                        </div>
                        <div className="p-col-12 p-md-6">
                            <h4>Created Date</h4>
                            <p>{actionDetails.created_on ?
                                DateTime.fromISO(actionDetails.created_on).toFormat('dd-MM-yyyy') :
                                'Unknown'}</p>
                        </div>
                        <div className="p-col-12 p-md-6">
                            <h4>Completed By</h4>
                            <p>{actionDetails.completed_by ?
                                assessorList.find(i => i.id === actionDetails.completed_by)?.information?.empname || 'Unknown' :
                                (actionDetails.modified_by ?
                                    assessorList.find(i => i.id === actionDetails.modified_by)?.information?.empname || 'Unknown' :
                                    'Unknown')}</p>
                        </div>
                        <div className="p-col-12 p-md-6">
                            <h4>Assigned To</h4>
                            <p>{actionDetails.assigned_to ?
                                assessorList.find(i => i.id === actionDetails.assigned_to)?.information?.empname || 'Unknown' :
                                'Not Assigned'}</p>
                        </div>
                        <div className="p-col-12 p-md-6">
                            <h4>Dealer</h4>
                            <p>{actionDetails.dealerName || 'NA'}</p>
                        </div>
                        <div className="p-col-12 p-md-6">
                            <h4>Location</h4>
                            <p>{actionDetails.dealerLocation || 'NA'}</p>
                        </div>
                        <div className="p-col-12 p-md-6">
                            <h4>Calibration ID</h4>
                            <p>{actionDetails.calibrationId || 'NA'}</p>
                        </div>
                        {actionDetails.comments && (
                            <div className="p-col-12">
                                <h4>Comments</h4>
                                <p>{actionDetails.comments}</p>
                            </div>
                        )}
                    </div>
                )}
            </Dialog>
        </>
    );
};

export default DealerActionCompletedTable;
