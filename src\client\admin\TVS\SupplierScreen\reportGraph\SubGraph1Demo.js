import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>hart,
  CartesianGrid,
  XAxis,
  YAxis,
  Tooltip,
  Bar,
  Cell,
  ResponsiveContainer,
  LabelList,
} from "recharts";

const SubGraph1Demo = ({ supplyData }) => {
  const [chartData, setChartData] = useState([]);

  const customTooltip = ({ payload }) => {
    if (!payload || payload.length === 0) return null;

    const { category, achievedScore } = payload[0].payload;

    let maxScoreValue = 0;
    if (category === "Environment") maxScoreValue = 37;
    else if (category === "Social") maxScoreValue =32.66;
    else if (category === "Governance") maxScoreValue = 30.33;

    return (
      <div
        className="custom-tooltip"
        style={{
          backgroundColor: "#fff",
          padding: "10px",
          borderRadius: "5px",
          border: "1px solid #ccc",
        }}
      >
        <h4>{category}</h4>
        <p>
          <strong>Achieved Score:</strong> {achievedScore}
        </p>
        <p>
          <strong>Maximum Score:</strong> {maxScoreValue}
        </p>
      </div>
    );
  };

  useEffect(() => {
    if (supplyData?.length > 0) {
      const getScoreByIds = (ids) =>
        supplyData
          .filter((item) => ids.includes(item.id))
          .reduce((sum, item) => sum + (item.sectionTotalScore || 0), 0);

      const totalEnv = getScoreByIds([2]);
      const totalSocial = getScoreByIds([3, 4, 7]);
      const totalGov = getScoreByIds([5, 6]);

      setChartData([
        {
          category: "Environment",
          achievedScore: (totalEnv/3).toFixed(1),
          maxScore: Math.max(0, 37 - totalEnv/3),
          achievedColor: "#2C7C69",
          maxColor: "#7FC8A9",
        },
        {
          category: "Social",
          achievedScore: (totalSocial/3).toFixed(1),
          maxScore: Math.max(0, 32.66 - totalSocial/3),
          achievedColor: "#FC6E51",
          maxColor: "#FEB2A8",
        },
        {
          category: "Governance",
          achievedScore: (totalGov/3).toFixed(1),
          maxScore: Math.max(0, 30.33 - totalGov/3),
          achievedColor: "#4A90E2",
          maxColor: "#AFCBFF",
        },
      ]);
    }
  }, [supplyData]);

  return (
    <div className="container mt-4" style={{ background: "#DAF3EF" }}>
      <h5 className="mb-3 text-center text-dark">ESG Score of Calibrated Suppliers</h5>

      <ResponsiveContainer width="100%" height={400}>
        <BarChart data={chartData} barSize={60} margin={{ top: 20, right: 30, left: 30, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="category" />
          <YAxis domain={[0, 50]} label={{ value: "Score", angle: -90, position: "insideLeft" }} />
          <Tooltip content={customTooltip} />

          {/* Achieved Score */}
          <Bar dataKey="achievedScore" stackId="a" name="Achieved Score">
            {chartData.map((entry, index) => (
              <Cell key={`cell-achieved-${index}`} fill={entry.achievedColor} />
            ))}
            <LabelList dataKey="achievedScore" position="insideBottom" style={{ fontSize: "12px", fill: "white" }} />
          </Bar>

          {/* Remaining to Max Score */}
          <Bar dataKey="maxScore" stackId="a" name="Max Score">
            {chartData.map((entry, index) => (
              <Cell key={`cell-max-${index}`} fill={entry.maxColor} />
            ))}
            <LabelList
              dataKey="maxScore"
              position="top"
              content={({ x, y, width, index }) => {
                const hardcodedMax = chartData[index].category === "Environment" ? 37 : chartData[index].category === "Social" ? 32.66 : 30.33;
                return (
                  <text x={x + width / 2} y={y - 10} textAnchor="middle" fill="black" fontSize="12px">
                    {hardcodedMax}
                  </text>
                );
              }}
            />
          </Bar>
        </BarChart>
      </ResponsiveContainer>

      {/* Legend */}
      <div className="legend" style={{ display: "flex", justifyContent: "center", marginTop: "10px" }}>
        {/* Maximum colours */}
        <div style={{ display: "flex", alignItems: "center", marginRight: "30px" }}>
          <span style={{ ...dotStyle, backgroundColor: "#7FC8A9" }} />
          <span style={{ ...dotStyle, backgroundColor: "#FEB2A8" }} />
          <span style={{ ...dotStyle, backgroundColor: "#AFCBFF" }} />
          <span style={{ marginLeft: "5px" }}>Maximum</span>
        </div>
        {/* Achieved colours */}
        <div style={{ display: "flex", alignItems: "center" }}>
          <span style={{ ...dotStyle, backgroundColor: "#2C7C69" }} />
          <span style={{ ...dotStyle, backgroundColor: "#FC6E51" }} />
          <span style={{ ...dotStyle, backgroundColor: "#4A90E2" }} />
          <span style={{ marginLeft: "5px" }}>Achieved</span>
        </div>
      </div>
    </div>
  );
};

// small helper style for legend dots
const dotStyle = {
  display: "inline-block",
  width: 10,
  height: 10,
  borderRadius: "50%",
  marginRight: 5,
};

export default SubGraph1Demo;
